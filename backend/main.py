# main.py
# Description: FastAPI backend for BaliBlissed AI features.
# This module provides API endpoints to handle AI-powered functionalities
# such as itinerary suggestions, user query responses, and contact form analysis.

# --- Imports ---
import logging
import os
from typing import Any

from dotenv import load_dotenv
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# --- Environment and Logging Configuration ---

# Load environment variables from a .env file for secure key management.
# This is crucial for keeping API keys out of the source code.
load_dotenv()

# Set up a logger for monitoring and debugging.
# Best practice: Use a configured logger instead of print() for server applications.
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Application Initialization ---

# Create a FastAPI application instance.
# Documentation is automatically generated at /docs and /redoc.
app = FastAPI(
    title="BaliBlissed AI Backend",
    description="Provides AI-powered services for the BaliBlissed Next.js application.",
    version="1.0.0",
)

# --- CORS (Cross-Origin Resource Sharing) Configuration ---

# To allow the Next.js frontend (running on a different port) to communicate
# with this backend, we need to enable CORS.
# Performance: Using a middleware is the standard and efficient way to handle this.
origins = [
    "http://localhost:3000",  # Next.js default development URL
    "http://127.0.0.1:3000",
    # Add production frontend URL here when deploying
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],  # Allows all standard HTTP methods
    allow_headers=["*"],  # Allows all headers
)

# --- Pydantic Models for Data Validation ---

# Pydantic models ensure that the data received by the API endpoints matches the expected structure.
# This provides automatic data validation and clear error messages for invalid requests.


# Model for the /suggest-itinerary endpoint
class ItineraryRequest(BaseModel):
    destination: str = Field(..., min_length=1, description="The travel destination.")
    duration: int = Field(..., gt=0, description="The duration of the trip in days.")
    interests: list[str] = Field(
        ...,
        min_items=1,
        description="A list of traveler's interests.",
    )


# Model for chat message history in /answer-query
class ChatMessage(BaseModel):
    role: str
    parts: list[dict[str, str]]


# Model for the /answer-query endpoint
class QueryRequest(BaseModel):
    query: str = Field(..., min_length=1, description="The user's query.")
    history: list[ChatMessage] = Field(default=[], description="The chat history.")


# Model for the /handle-contact-inquiry endpoint
class ContactInquiryRequest(BaseModel):
    name: str = Field(..., min_length=1, description="The user's name.")
    email: str = Field(..., description="The user's email address.")
    message: str = Field(..., min_length=10, description="The user's message.")


# --- API Endpoints ---


@app.get("/")
def read_root() -> dict[str, Any]:
    """Root endpoint to check if the server is running."""
    return {"status": "ok", "message": "Welcome to the BaliBlissed AI Backend!"}


@app.post("/api/suggest-itinerary")
async def suggest_itinerary(request: ItineraryRequest) -> dict[str, Any]:
    """Generate a travel itinerary based on user preferences.

    This endpoint mirrors the functionality of 'suggest-itinerary.ts'.
    It uses a generative AI model to create a custom itinerary.
    """

    logger.info(f"Received itinerary request for {request.destination}")
    try:
        # --- AI Integration Placeholder ---
        # Here, you would integrate with a generative AI model (e.g., Google's Gemini).
        # The API key should be loaded securely from environment variables.
        #
        # Example with google-generativeai:
        #
        # import google.generativeai as genai
        # genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
        # model = genai.GenerativeModel('gemini-pro')
        # prompt = f"Create a detailed {request.duration}-day travel itinerary for {request.destination}..."
        # response = await model.generate_content_async(prompt)
        # return {"itinerary": response.text}
        #
        # For now, a dummy response is returned for demonstration purposes.
        dummy_itinerary = (
            f"### **Your Custom Itinerary for {request.destination}**\n\n"
            f"**Duration:** {request.duration} days\n"
            f"**Interests:** {', '.join(request.interests)}\n\n"
            "This is a placeholder itinerary generated by the FastAPI backend. "
            "Integrate a real AI model to get a complete suggestion."
        )
        return {"itinerary": dummy_itinerary}

    except Exception as e:
        logger.exception("Error in /suggest-itinerary")
        # Best practice: Return a structured error response.
        raise HTTPException(
            status_code=500,
            detail="Failed to generate itinerary.",
        ) from e


@app.post("/api/answer-query")
async def answer_query(request: QueryRequest) -> dict[str, Any]:
    """Answer a user's query, maintaining chat history context.

    This endpoint mirrors the functionality of 'answer-query.ts'.
    """
    logger.info(f"Received query: {request.query}")
    try:
        # --- AI Integration Placeholder ---
        # A real implementation would use the query and history to get a response
        # from a conversational AI model.
        dummy_answer = (
            f"This is a dummy answer from the FastAPI backend for your query: '{request.query}'.\n"
            f"The chat history has {len(request.history)} messages."
        )
        return {"answer": dummy_answer}

    except Exception as e:
        logger.exception(f"Error in /answer-query: {e}")
        raise HTTPException(status_code=500, detail="Failed to answer query.") from e


@app.post("/api/handle-contact-inquiry")
async def handle_contact_inquiry(request: ContactInquiryRequest) -> dict[str, Any]:
    """Analyze a contact inquiry to categorize it and provide a summary.

    This endpoint mirrors the functionality of 'handle-contact-inquiry.ts'.
    """
    logger.info(f"Received contact inquiry from {request.name}")
    try:
        # --- AI Integration Placeholder ---
        # This would use an AI model to perform text analysis on the user's message.
        dummy_analysis = {
            "summary": f"A summary of the message from {request.name} would go here.",
            "category": "General Inquiry",
            "suggested_reply": "Thank you for contacting us! We will get back to you shortly.",
        }
        return {"analysis": dummy_analysis}

    except Exception as e:
        logger.exception(f"Error in /handle-contact-inquiry: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle inquiry.") from e


# --- Error Handling Middleware ---


# Best practice: A generic error handler to catch any unhandled exceptions
# and return a consistent JSON error response.
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    logger.error(f"Unhandled exception for request {request.url.path}: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "An unexpected server error occurred."},
    )
