# This file is being contributed to pyasn1-modules software.
#
# Created by <PERSON> with assistance from the asn1ate tool.
# Auto-generated by asn1ate v.0.6.0 from rfc8103.asn.
#
# Copyright (c) 2019, Vigil Security, LLC
# License: http://snmplabs.com/pyasn1/license.html
#
# ChaCha20Poly1305 algorithm fo use with the Authenticated-Enveloped-Data
# protecting content type for the Cryptographic Message Syntax (CMS)
#
# ASN.1 source from:
# https://www.rfc-editor.org/rfc/rfc8103.txt

from pyasn1.type import constraint
from pyasn1.type import univ


def _OID(*components):
    output = []
    for x in tuple(components):
        if isinstance(x, univ.ObjectIdentifier):
            output.extend(list(x))
        else:
            output.append(int(x))

    return univ.ObjectIdentifier(output)


class AEADChaCha20Poly1305Nonce(univ.OctetString):
    pass


AEADChaCha20Poly1305Nonce.subtypeSpec = constraint.ValueSizeConstraint(12, 12)

id_alg_AEADChaCha20Poly1305 = _OID(1, 2, 840, 113549, 1, 9, 16, 3, 18)
